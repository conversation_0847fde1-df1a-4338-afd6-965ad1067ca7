<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversion-Boost Analyse – 111 Medien Service</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&family=Inter:wght@300;400;500;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
            color: #D8D8D8;
            line-height: 1.7; /* Improved from 1.6 */
            overflow-x: hidden;
            font-size: 16px;
            letter-spacing: 0.01em;
        }
        
        .slide {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            margin: 0;
            padding: 60px;
            padding-top: 140px; /* Increased top padding to account for header */
            min-height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-start; /* Changed from center to flex-start */
            border-bottom: 1px solid #333;
            page-break-after: always;
        }
        
        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(13, 110, 253, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(77, 184, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(13, 110, 253, 0.03) 0%, transparent 70%),
                linear-gradient(135deg, rgba(13, 110, 253, 0.02) 0%, transparent 50%, rgba(77, 184, 255, 0.02) 100%);
            pointer-events: none;
            animation: ambientPulse 8s ease-in-out infinite alternate;
        }

        @keyframes ambientPulse {
            0% {
                opacity: 0.8;
                transform: scale(1);
            }
            100% {
                opacity: 1;
                transform: scale(1.02);
            }
        }
        
        .slide-content {
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .slide h1 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 800;
            font-size: 3.5em;
            background: linear-gradient(135deg, #0d6efd, #4db8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .slide h2 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 2.5em;
            color: #0d6efd;
            margin-bottom: 60px; /* Increased from 40px */
            margin-top: 20px; /* Added top margin for better spacing */
            text-align: center;
            position: relative;
        }
        
        .slide h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #0d6efd, #4db8ff);
            border-radius: 2px;
        }
        
        .slide h3 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 2em; /* Increased from 1.8em */
            color: #4db8ff;
            margin-bottom: 35px; /* Increased from 25px */
            line-height: 1.3;
            letter-spacing: -0.02em;
        }

        .subtitle {
            font-size: 1.5em; /* Increased from 1.4em */
            color: #D8D8D8; /* Better projector readability */
            text-align: center;
            margin-bottom: 60px; /* Increased from 50px */
            font-weight: 300;
            line-height: 1.4;
            letter-spacing: 0.01em;
        }
        
        .header-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(90deg, #0d6efd, #1a1a1a);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 60px;
            z-index: 10;
            box-shadow:
                0 4px 20px rgba(13, 110, 253, 0.2),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .header-bar::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.5), transparent);
        }
        
        .logo {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 1.2em;
            color: white;
            background: rgba(255,255,255,0.1);
            padding: 10px 20px;
            border-radius: 25px;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }
        
        .date {
            color: #b0b0b0;
            font-size: 0.95em;
            font-weight: 500;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin-top: 50px; /* Increased from 40px */
            align-items: start;
        }

        /* Responsive design improvements */
        @media (max-width: 1200px) {
            .slide {
                padding: 40px;
                padding-top: 120px;
            }

            .two-column {
                gap: 40px;
            }
        }

        @media (max-width: 768px) {
            .slide {
                padding: 30px;
                padding-top: 110px;
            }

            .slide h1 {
                font-size: 2.5em;
            }

            .slide h2 {
                font-size: 2em;
                margin-bottom: 40px;
            }

            .slide h3 {
                font-size: 1.6em;
            }

            .two-column {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .header-bar {
                padding: 0 30px;
            }

            .logo {
                font-size: 1em;
                padding: 8px 16px;
            }
        }
        
        .website-analysis {
            background: rgba(13, 110, 253, 0.1);
            border: 1px solid rgba(13, 110, 253, 0.3);
            border-radius: 20px;
            padding: 35px;
            -webkit-backdrop-filter: blur(15px);
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(13, 110, 253, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .website-analysis::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.5), transparent);
        }
        
        .website-analysis h4 {
            color: #4db8ff;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .website-section {
            background: rgba(255,255,255,0.05);
            padding: 15px 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 3px solid #0d6efd;
        }
        
        .website-section .section-name {
            color: #4db8ff;
            font-weight: 600;
            font-size: 0.95em;
        }
        
        .website-section .section-desc {
            color: #b0b0b0;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .finding {
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(13, 110, 253, 0.25);
            padding: 30px;
            border-radius: 16px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.15),
                0 1px 3px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .finding:hover {
            transform: translateY(-2px);
            box-shadow:
                0 8px 30px rgba(0, 0, 0, 0.2),
                0 4px 12px rgba(13, 110, 253, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .finding::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(180deg, #0d6efd, #4db8ff);
            box-shadow: 0 0 10px rgba(13, 110, 253, 0.5);
        }
        
        .finding h4 {
            color: #4db8ff;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 15px; /* Increased from 12px */
            font-size: 1.2em; /* Increased from 1.1em */
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .finding p {
            color: #D8D8D8; /* Better projector readability */
            line-height: 1.6; /* Improved from 1.5 */
            font-size: 1.05em;
            letter-spacing: 0.01em;
        }
        
        .icon-list {
            list-style: none;
            padding: 0;
            display: grid;
            gap: 25px;
        }
        
        .icon-list li {
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(13, 110, 253, 0.25);
            padding: 35px;
            border-radius: 20px;
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow:
                0 4px 20px rgba(0, 0, 0, 0.1),
                0 1px 3px rgba(0, 0, 0, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .icon-list li::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 80% 20%, rgba(13, 110, 253, 0.05) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .icon-list li:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 8px 16px rgba(13, 110, 253, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .icon-list li:hover::after {
            opacity: 1;
        }
        
        .icon-list li::before {
            content: attr(data-icon);
            font-size: 2.5em;
            position: absolute;
            right: 30px;
            top: 30px;
            opacity: 0.7;
        }
        
        .icon-list li h4 {
            color: #4db8ff;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 18px; /* Increased from 15px */
            font-size: 1.3em; /* Increased from 1.2em */
            max-width: 70%;
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .icon-list li p {
            color: #D8D8D8; /* Better projector readability */
            line-height: 1.7; /* Improved from 1.6 */
            max-width: 75%;
            font-size: 1.05em;
            letter-spacing: 0.01em;
        }
        
        .timeline {
            margin: 40px 0;
        }
        
        .timeline-item {
            background: rgba(255,255,255,0.05);
            border: 1px solid rgba(13, 110, 253, 0.2);
            padding: 35px;
            margin: 30px 0;
            border-radius: 15px;
            position: relative;
            margin-left: 60px;
        }
        
        .timeline-item::before {
            content: attr(data-week);
            background: linear-gradient(135deg, #0d6efd, #4db8ff);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            position: absolute;
            left: -60px;
            top: 35px;
            font-weight: 600;
            font-size: 0.9em;
            white-space: nowrap;
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        }
        
        .timeline-item h4 {
            color: #4db8ff;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .timeline-item ul {
            color: #b0b0b0;
            margin-left: 20px;
        }
        
        .timeline-item li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: rgba(255,255,255,0.08);
            border-radius: 20px;
            overflow: hidden;
            margin: 30px 0;
            -webkit-backdrop-filter: blur(15px);
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.15),
                0 2px 8px rgba(13, 110, 253, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(13, 110, 253, 0.2);
        }
        
        th {
            background: linear-gradient(135deg, #0d6efd, #4db8ff);
            color: white;
            padding: 20px;
            text-align: left;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            font-size: 1.1em;
        }
        
        td {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 1.05em;
        }
        
        tr:nth-child(even) td {
            background: rgba(255,255,255,0.02);
        }
        
        .kpi-highlight {
            margin: 30px 0;
            padding: 25px;
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 12px;
        }
        
        .kpi-highlight h4 {
            color: #ffc107;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .kpi-highlight ul {
            color: #e0e0e0;
            margin-left: 20px;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
            display: grid;
            gap: 20px;
        }
        
        .checklist li {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            padding: 25px;
            border-radius: 12px;
            position: relative;
            padding-left: 70px;
        }
        
        .checklist li::before {
            content: "✓";
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 25px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .checklist li h4 {
            color: #20c997;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .checklist li p {
            color: #b0b0b0;
            line-height: 1.5;
        }
        
        .cta-section {
            text-align: center;
            margin: 50px 0;
        }
        
        .cta-button {
            background: linear-gradient(135deg, #0d6efd, #4db8ff);
            color: white;
            padding: 18px 40px;
            border-radius: 50px;
            text-decoration: none;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            display: inline-block;
            margin: 30px 0;
            font-size: 1.2em;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 10px 30px rgba(13, 110, 253, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(13, 110, 253, 0.4);
        }
        
        .contact-info {
            background: rgba(255,255,255,0.08);
            border: 1px solid rgba(13, 110, 253, 0.25);
            padding: 50px;
            border-radius: 25px;
            text-align: center;
            -webkit-backdrop-filter: blur(15px);
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.15),
                0 2px 8px rgba(13, 110, 253, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .contact-info h2 {
            margin-bottom: 35px !important;
        }

        .contact-info h3 {
            color: #4db8ff !important;
            margin-bottom: 30px !important;
        }

        .team-info {
            margin: 0;
            font-size: 1.1em;
        }

        .team-subtitle {
            margin: 15px 0 0 0;
            opacity: 0.8;
        }

        .company-info {
            margin-top: 35px;
            opacity: 0.8;
        }

        .company-name {
            margin: 8px 0;
            font-size: 1.1em;
        }

        .company-tagline {
            margin: 0;
            color: #b0b0b0;
        }
        
        .team-placeholder {
            background: rgba(13, 110, 253, 0.1);
            border: 2px dashed rgba(13, 110, 253, 0.3);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            color: #4db8ff;
        }
        
        .slide-number {
            position: absolute;
            bottom: 30px;
            right: 60px;
            color: #666;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .title-slide {
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 60px !important; /* Override the increased padding for title slide */
        }
        
        .title-slide .slide-content {
            max-width: 800px;
        }
        
        .wave-decoration {
            position: absolute;
            bottom: 10%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 3em;
            opacity: 0.1;
            color: #0d6efd;
        }
        
        @media print {
            body { background: #000; }
            .slide { 
                box-shadow: none; 
                page-break-after: always;
                min-height: 100vh;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .slide-content > * {
            animation: fadeInUp 0.8s ease-out;
        }
    </style>
</head>
<body>

<!-- SLIDE 1: Titel -->
<div class="slide title-slide">
    <div class="header-bar">
        <div class="logo">111 Medien Service</div>
        <div class="date">Januar 2025</div>
    </div>
    
    <div class="slide-content">
        <h1>Conversion-Boost Analyse</h1>
        <p class="subtitle">Schneller mehr Leads aus bestehenden Besuchern</p>
        <div class="wave-decoration">📺 ~ ~ ~</div>
    </div>
    <div class="slide-number">01</div>
</div>

<!-- SLIDE 2: Ist-Zustand -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>Ist-Zustand auf einen Blick</h2>
        
        <div style="display: flex; flex-direction: column; align-items: center; gap: 40px; max-width: 1000px; margin: 0 auto;">
            <!-- Hero Section Screenshot -->
            <div style="background: rgba(255,255,255,0.08); border: 2px solid rgba(13, 110, 253, 0.3); border-radius: 15px; padding: 20px; text-align: center; color: #4db8ff; width: 100%; max-width: 600px;">
                <p style="margin: 0; font-size: 1.1em; font-weight: 600;">🖥️ Hero Section - Live Website</p>
                <div style="border-radius: 10px; overflow: hidden; box-shadow: 0 8px 32px rgba(13, 110, 253, 0.2); margin-top: 15px;">
                    <img src="Herosection.png" alt="111 Medien Service Hero Section" style="width: 100%; height: auto; display: block; border-radius: 10px;">
                </div>
            </div>

            <div style="width: 100%;">
                <h3 style="text-align: center; margin-bottom: 30px;">🚨 5 Kritische Findings (Live-Website Analyse)</h3>

                <div class="finding">
                    <h4>❌ USP völlig unklar: "Wir lösen das für Sie"</h4>
                    <p>Headline ist zu vage. Besucher verstehen nicht sofort: "50% TV-Werbekosten sparen durch Last-Minute-Buchungen". Konkrete Nutzen fehlen.</p>
                </div>

                <div class="finding">
                    <h4>💸 Keine Preisbeweise → "50% sparen" wirkt unglaubwürdig</h4>
                    <p>Behauptung "bis zu 50% Werbekosten sparen" ohne sofortige Beispiele oder Vergleichsrechnung. Fehlt: "Normalpreis €X → Unser Preis €Y".</p>
                </div>

                <div class="finding">
                    <h4>🎯 Schwacher CTA: "Mehr Infos" statt "Jetzt 50% sparen"</h4>
                    <p>Primärer CTA ist nicht handlungsorientiert. Fehlt: Urgency, konkrete Aktion, Nutzenversprechen. Social Proof zu schwach positioniert.</p>
                </div>

                <div class="finding">
                    <h4>📱 Mobile Experience & Page Speed Probleme</h4>
                    <p>Große Bilder, potentielle Ladezeit-Issues. Mobile Optimierung fraglich. Conversion-Killer für 60%+ mobile Traffic.</p>
                </div>

                <div class="finding">
                    <h4>⏰ Keine Urgency: Last-Minute = Zeitdruck fehlt</h4>
                    <p>Kerngeschäft sind kurzfristige Buchungen, aber Website vermittelt keine Dringlichkeit. Fehlt: "Nur noch X Plätze verfügbar".</p>
                </div>
            </div>
        </div>
    </div>
    <div class="slide-number">02</div>
</div>

<!-- SLIDE 3: Quick Wins -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>⚡ Quick Wins (Woche 1)</h2>
        
        <ul class="icon-list">
            <li data-icon="🎯">
                <h4>Neue Hero-Headline mit klarem USP</h4>
                <p>"TV-Reichweite wie eine Luxusreise – zum Last-Minute-Preis" + Unterzeile "75% günstiger als Listenpreis dank Restplatz-Buchungen"</p>
            </li>
            
            <li data-icon="💰">
                <h4>Preis-Teaser-Box mit konkreten Zahlen</h4>
                <p>"Spot normal 7.500€ → bei uns ab 2.500€ / 470.000 Views" - Glaubwürdigkeit durch Transparenz</p>
            </li>
            
            <li data-icon="📈">
                <h4>Kontrastfarbe CTA in Blau</h4>
                <p>Primärer CTA in Kontrastfarbe, Wiederholung nach jedem Hauptabschnitt + Fly-in "Fordern Sie 3 Preisbeispiele an"</p>
            </li>
            
            <li data-icon="🏆">
                <h4>"Wall of Trust" direkt unter Hero</h4>
                <p>Kundenlogos + Mini-Case "38% ROAS-Steigerung" für sofortiges Vertrauen</p>
            </li>

            <li data-icon="⚡">
                <h4>Urgency-Element: "Last-Minute = Zeitdruck"</h4>
                <p>"Nur noch 3 Restplätze diese Woche verfügbar" + Mobile-optimierte CTAs für 60%+ mobile Nutzer</p>
            </li>
        </ul>
    </div>
    <div class="slide-number">03</div>
</div>

<!-- SLIDE 4: Phase-Plan -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>📅 Phase-Plan (3 Wochen - Je 1 Woche pro Phase)</h2>

        <div class="timeline">
            <div class="timeline-item" data-week="📅 W1">
                <h4>Woche 1: Messaging-Sprint & Hero Relaunch</h4>
                <ul>
                    <li><strong>Neue Hero-Kombi:</strong> "TV-Reichweite wie eine Luxusreise – zum Last-Minute-Preis"</li>
                    <li><strong>Unterzeile:</strong> "75% günstiger als Listenpreis dank Restplatz-Buchungen"</li>
                    <li><strong>Preis-Teaser-Box:</strong> Konkrete Zahlen für Glaubwürdigkeit</li>
                    <li><strong>CTA-Optimierung:</strong> "Jetzt 50% sparen" statt "Mehr Infos"</li>
                </ul>
            </div>

            <div class="timeline-item" data-week="📅 W2">
                <h4>Woche 2: Technical Optimization & Mobile Experience</h4>
                <ul>
                    <li><strong>Pagespeed-Fixes:</strong> WebP + loading="lazy" + WP Rocket/FlyingPress → ~35% schneller</li>
                    <li><strong>Mobile Optimierung:</strong> Responsive Design & Touch-optimierte CTAs</li>
                    <li><strong>Urgency-Elemente:</strong> "Nur noch X Plätze verfügbar" Timer/Counter</li>
                    <li><strong>Social Proof:</strong> Erweiterte Kundenlogos und Case Studies</li>
                </ul>
            </div>

            <div class="timeline-item" data-week="📅 W3">
                <h4>Woche 3: Analytics, Testing & Lead-Magnet Setup</h4>
                <ul>
                    <li><strong>GA4 + Tag Manager:</strong> Events (cta_click, form_submit) für präzise Messung</li>
                    <li><strong>Hotjar Heatmap:</strong> User-Behaviour-Tracking für weitere Optimierungen</li>
                    <li><strong>A/B Test:</strong> Google Optimize Hero vs. Control für datenbasierte Entscheidungen</li>
                    <li><strong>Lead-Magnet:</strong> "Gratis Checkliste TV-Restplatz buchen" Integration</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="slide-number">04</div>
</div>

<!-- SLIDE 5: KPIs -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>📊 Messbare KPIs & Erfolgskontrolle</h2>
        
        <table>
            <thead>
                <tr>
                    <th>Key Performance Indicator</th>
                    <th>Aktueller Wert</th>
                    <th>Ziel nach 14 Tagen</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Hero-CTR</strong> (Click-Through-Rate)</td>
                    <td>0,8%</td>
                    <td><strong>≥ 1,2%</strong> (+50%)</td>
                </tr>
                <tr>
                    <td><strong>Lead-Form-CR</strong> (Conversion-Rate)</td>
                    <td>0,9%</td>
                    <td><strong>≥ 1,3%</strong> (+44%)</td>
                </tr>
                <tr>
                    <td><strong>LCP</strong> (Largest Contentful Paint)</td>
                    <td>3,1 s</td>
                    <td><strong>≤ 2,5 s</strong> (-19%)</td>
                </tr>
            </tbody>
        </table>
        
        <div class="kpi-highlight">
            <h4>🎯 Warum diese KPIs kritisch sind:</h4>
            <ul>
                <li><strong>Conversion-Hemmnis:</strong> Nutzen bleibt abstrakt → Besucher verstehen nicht sofort den Mehrwert</li>
                <li><strong>Fehlende Datenbasis:</strong> Kein GA4-Event oder Consent-Banner sichtbar; keine Experimente</li>
                <li><strong>Core Web Vitals-Risiko:</strong> Mobile Bounce steigt. Core Web Vitals-Risiko durch LCP > 600kB, kein lazy loading der unteren Bilder</li>
            </ul>
        </div>
    </div>
    <div class="slide-number">05</div>
</div>

<!-- SLIDE 6: Nächste Schritte -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>✅ Nächste Schritte zum Go-Live</h2>
        
        <ul class="checklist">
            <li>
                <h4>1. Konzept-Freigabe im Call (30 Min)</h4>
                <p>Finale Abstimmung der Headlines, Preis-Teaser und CTA-Texte. Klärung technischer Details und Timeline-Bestätigung.</p>
            </li>
            
            <li>
                <h4>2. Asset-Bereitstellung (Logos, Cases, Bilder)</h4>
                <p>Hochauflösende Kundenlogos, Case-Study-Daten (ROAS-Zahlen), Referenz-Screenshots für Social Proof Wall.</p>
            </li>
            
            <li>
                <h4>3. Technische Umsetzung & Testing</h4>
                <p>Hero-Relaunch, Pagespeed-Optimierung, GA4-Events, Lead-Magnet-Integration. Staging-Umgebung für finale Freigabe.</p>
            </li>
            
            <li>
                <h4>4. Go-Live & KPI-Monitoring</h4>
                <p>Launch mit A/B-Test-Setup. KPI-Report nach 14 Tagen mit Conversion-Analyse und Empfehlungen für Phase 2.</p>
            </li>
        </ul>
    </div>
    <div class="slide-number">06</div>
</div>

<!-- SLIDE 7: Inspiration-Based Suggestions -->
<div class="slide" style="padding-top: 60px;">
    <div class="slide-content">
        <h2>🔍 Inspiration-Based Suggestions</h2>
        <p class="subtitle">Basierend auf Best Practices von hochkonvertierenden Landing Pages</p>

        <div style="max-width: 1000px; margin: 0 auto;">
            <ul class="icon-list">
                <li data-icon="🎯">
                    <h4>Headline mit klarem Kundennutzen statt Servicebeschreibung</h4>
                    <p>Statt "Wir lösen das für Sie" → "Sparen Sie 50% TV-Werbekosten durch Last-Minute-Buchungen". Der Kunde muss sofort verstehen: Was habe ich davon?</p>
                </li>

                <li data-icon="🔄">
                    <h4>Visueller "How It Works" Prozess mit Icons</h4>
                    <p>3-Schritt-Prozess: "1. Anfrage senden → 2. Angebote erhalten → 3. Buchen & sparen". Macht den Service greifbar und reduziert Unsicherheit.</p>
                </li>

                <li data-icon="💡">
                    <h4>Homepage-Flow um spezifisches Kundenproblem strukturieren</h4>
                    <p>Aufbau: Problem ("TV-Werbung zu teuer?") → Lösung ("Last-Minute-Restplätze") → Beweis (Kundenbeispiele) → Aktion (CTA). Klare Storyline statt Feature-Liste.</p>
                </li>

                <li data-icon="🚀">
                    <h4>Starke, gut platzierte Call-to-Action Buttons</h4>
                    <p>Primärer CTA in Kontrastfarbe, wiederholt nach jedem Hauptabschnitt. Text: "Jetzt 50% sparen" statt "Mehr Infos". Urgency: "Nur noch 3 Plätze diese Woche".</p>
                </li>

                <li data-icon="⭐">
                    <h4>Echte, spezifische Testimonials mit messbaren Ergebnissen</h4>
                    <p>Statt allgemeine Aussagen → "Dank 111 Medien Service haben wir 38% mehr ROAS erreicht und 4.200€ gespart" - Max Müller, Marketing Director, TechCorp GmbH</p>
                </li>

                <li data-icon="🎯">
                    <h4>Ablenkungen minimieren - Fokus auf Conversion</h4>
                    <p>Blog-Previews und unrelated Links entfernen oder nach unten verschieben. Jedes Element muss zur Conversion beitragen oder weg. Weniger ist mehr.</p>
                </li>
            </ul>

            <div class="kpi-highlight" style="margin-top: 50px;">
                <h4>💡 Warum diese Prinzipien funktionieren:</h4>
                <ul>
                    <li><strong>Klarheit vor Kreativität:</strong> Besucher entscheiden in 3 Sekunden - der Nutzen muss sofort erkennbar sein</li>
                    <li><strong>Vertrauen durch Beweise:</strong> Konkrete Zahlen und Ergebnisse schlagen vage Versprechen</li>
                    <li><strong>Psychologie der Dringlichkeit:</strong> Last-Minute-Geschäft braucht Last-Minute-Gefühl auf der Website</li>
                    <li><strong>Mobile-First-Denken:</strong> 60%+ der Besucher sind mobil - jeder CTA muss thumb-friendly sein</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="slide-number">07</div>
</div>

<!-- SLIDE 8: Kontakt & CTA -->
<div class="slide">
    <div class="header-bar">
        <div class="logo">111 Medien Service</div>
        <div class="date">Januar 2025</div>
    </div>

    <div class="slide-content">
        <div class="contact-info">
            <h2>🚀 Bereit für den Conversion-Boost?</h2>

            <h3>Ihr Conversion-Optimierung Team</h3>

            <div class="team-placeholder">
                <p class="team-info"><strong>👥 Laura + Amer</strong></p>
                <p class="team-subtitle">Ihre Ansprechpartner für Performance Marketing & CRO</p>
            </div>

            <div class="cta-section">
                <a href="#" class="cta-button">Jetzt Conversion-Boost starten</a>
            </div>

            <div class="company-info">
                <p class="company-name"><strong>111 Medien Service</strong></p>
                <p class="company-tagline">Ihr Partner für Performance Marketing & TV-Werbung</p>
            </div>
        </div>
    </div>
    <div class="slide-number">08</div>
</div>

</body>
</html>